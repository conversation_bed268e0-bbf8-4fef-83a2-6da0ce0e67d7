#!/bin/bash

# Setup NotePlan Auto-Sync to start automatically on macOS

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLIST_FILE="$HOME/Library/LaunchAgents/com.noteplan.rag.sync.plist"

# Create the LaunchAgent plist file
cat > "$PLIST_FILE" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.noteplan.rag.sync</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/python3</string>
        <string>$SCRIPT_DIR/noteplan_watcher.py</string>
        <string>watch</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$HOME/.noteplan_rag_sync.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/.noteplan_rag_sync.error.log</string>
    <key>WorkingDirectory</key>
    <string>$SCRIPT_DIR</string>
</dict>
</plist>
EOF

echo "📝 Created LaunchAgent: $PLIST_FILE"

# Load the LaunchAgent
launchctl load "$PLIST_FILE"

echo "✅ NotePlan Auto-Sync is now set to start automatically!"
echo "🔄 Starting service now..."

# Start the service
launchctl start com.noteplan.rag.sync

echo ""
echo "🎯 Setup complete! Your NotePlan notes will now be automatically indexed."
echo ""
echo "Useful commands:"
echo "  launchctl stop com.noteplan.rag.sync     # Stop the service"
echo "  launchctl start com.noteplan.rag.sync    # Start the service"
echo "  launchctl unload $PLIST_FILE             # Remove auto-start"
echo "  tail -f ~/.noteplan_rag_sync.log         # View logs"
