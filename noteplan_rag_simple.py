#!/usr/bin/env python3
"""
NotePlan RAG System - Simple Version
A Retrieval-Augmented Generation system for your NotePlan notes using local LLMs.
This version uses the existing llm tool for embeddings and similarity search.
"""

import os
import sys
import sqlite3
import subprocess
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configuration
NOTEPLAN_PATH = "/Users/<USER>/Library/Containers/co.noteplan.NotePlan3/Data/Library/Application Support/co.noteplan.NotePlan3/Notes"
RAG_DB = "noteplan_rag_simple.db"
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
DEFAULT_LLM = "q3"  # Your Qwen3 model alias

class NotePlanRAG:
    def __init__(self, db_path: str = RAG_DB):
        self.db_path = db_path
        self.conn = None
        self._init_db()
    
    def _init_db(self):
        """Initialize the database"""
        self.conn = sqlite3.connect(self.db_path)
        
        # Create notes table for metadata
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS notes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                note_path TEXT UNIQUE,
                note_title TEXT,
                note_type TEXT,
                content_hash TEXT,
                last_modified TEXT,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        self.conn.commit()
        print("✅ Database initialized")
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash of content for change detection"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _extract_title(self, content: str, file_path: str) -> str:
        """Extract title from note content or use filename"""
        lines = content.strip().split('\n')
        
        # Look for markdown title
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        
        # Use filename as fallback
        return Path(file_path).stem
    
    def embed_note(self, file_path: str) -> bool:
        """Process and embed a single note file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Failed to read {file_path}: {e}")
            return False
        
        if not content.strip():
            return True  # Skip empty files
        
        # Get metadata
        path_obj = Path(file_path)
        relative_path = str(path_obj.relative_to(NOTEPLAN_PATH))
        note_type = path_obj.suffix[1:] if path_obj.suffix else 'txt'
        content_hash = self._get_content_hash(content)
        title = self._extract_title(content, file_path)
        last_modified = str(path_obj.stat().st_mtime)
        
        # Check if already processed and unchanged
        cursor = self.conn.execute(
            "SELECT content_hash FROM notes WHERE note_path = ?",
            (relative_path,)
        )
        existing = cursor.fetchone()
        
        if existing and existing[0] == content_hash:
            return True  # Already up to date
        
        # Create a clean ID for the embedding
        note_id = relative_path.replace('/', '_').replace(' ', '_').replace('.', '_')
        
        # Embed using llm tool
        cmd = [
            "llm", "embed", 
            "-m", EMBEDDING_MODEL,
            "-d", self.db_path.replace('.db', '_embeddings.db'),
            "--store",
            "-c", content,
            "notes", note_id
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Failed to embed {relative_path}: {e}")
            return False
        
        # Store metadata in our database
        self.conn.execute("""
            INSERT OR REPLACE INTO notes(
                note_path, note_title, note_type, 
                content_hash, last_modified, content
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            relative_path, title, note_type,
            content_hash, last_modified, content
        ))
        
        self.conn.commit()
        return True
    
    def embed_all_notes(self) -> bool:
        """Embed all notes in the NotePlan directory"""
        print("🔍 Scanning NotePlan directory...")
        
        notes_path = Path(NOTEPLAN_PATH)
        if not notes_path.exists():
            print(f"❌ NotePlan directory not found: {NOTEPLAN_PATH}")
            return False
        
        # Find all markdown and text files
        note_files = []
        for ext in ['*.md', '*.txt']:
            note_files.extend(notes_path.rglob(ext))
        
        print(f"📝 Found {len(note_files)} notes to process...")
        
        success_count = 0
        for i, note_file in enumerate(note_files, 1):
            relative_path = note_file.relative_to(notes_path)
            print(f"⚡ Processing {i}/{len(note_files)}: {relative_path}")
            
            if self.embed_note(str(note_file)):
                success_count += 1
            else:
                print(f"⚠️  Failed to process {relative_path}")
        
        print(f"✅ Successfully processed {success_count}/{len(note_files)} notes!")
        return True
    
    def search_notes(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant notes using vector similarity"""
        print(f"🔍 Searching for: '{query}'")
        
        # Use llm similar command
        cmd = [
            "llm", "similar", "notes",
            "-d", self.db_path.replace('.db', '_embeddings.db'),
            "-c", query,
            "-n", str(limit)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            # Parse the output - it might be multiple JSON objects, one per line
            similar_results = []
            if result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        try:
                            similar_results.append(json.loads(line))
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
        
        # Get full note details from our metadata database
        results = []
        for item in similar_results:
            note_id = item.get('id', '')
            # Convert back to path - note_id format is like "Strategy_Offsite_md"
            # Convert underscores back to spaces and add extension
            parts = note_id.split('_')
            if parts[-1] in ['md', 'txt']:
                extension = parts[-1]
                name_parts = parts[:-1]
                note_name = ' '.join(name_parts) + '.' + extension
            else:
                note_name = note_id.replace('_', ' ')

            cursor = self.conn.execute(
                "SELECT note_path, note_title, note_type, content FROM notes WHERE note_path LIKE ? OR note_title LIKE ?",
                (f"%{note_name}%", f"%{' '.join(parts)}%")
            )
            note_data = cursor.fetchone()

            if note_data:
                results.append({
                    'path': note_data[0],
                    'title': note_data[1],
                    'type': note_data[2],
                    'content': note_data[3],
                    'score': item.get('score', 0)
                })
            else:
                # If we can't find the note in our metadata, use the content from the embedding
                results.append({
                    'path': note_id,
                    'title': note_id.replace('_', ' '),
                    'type': 'unknown',
                    'content': item.get('content', ''),
                    'score': item.get('score', 0)
                })
        
        return results
    
    def ask_with_context(self, question: str, model: str = DEFAULT_LLM, limit: int = 3) -> str:
        """Ask a question with relevant note context"""
        print(f"🤔 Question: {question}")
        
        # Get relevant context
        results = self.search_notes(question, limit)
        
        if not results:
            print("⚠️  No relevant context found, asking without RAG...")
            prompt = question
        else:
            print(f"📚 Found {len(results)} relevant notes from your collection!")
            
            # Build context from results
            context_parts = []
            for i, result in enumerate(results, 1):
                context_parts.append(f"""
Note {i}: {result['title']} ({result['path']})
{result['content'][:1000]}{'...' if len(result['content']) > 1000 else ''}
""")
            
            context = "\n".join(context_parts)
            
            prompt = f"""Based on the following context from my personal NotePlan notes, please answer this question: {question}

Context from my notes:
{context}

Please provide a helpful answer based on this context. If the context doesn't contain relevant information, please say so and provide general guidance."""
        
        # Ask the LLM
        cmd = ["llm", "-m", model, prompt]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"❌ LLM query failed: {e}")
            return ""
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the processed notes"""
        cursor = self.conn.execute("""
            SELECT 
                COUNT(*) as total_notes,
                note_type,
                COUNT(*) as count_by_type
            FROM notes
            GROUP BY note_type
        """)
        
        stats = {"total_notes": 0, "by_type": {}}
        total = 0
        for row in cursor.fetchall():
            if row[1]:  # note_type
                stats["by_type"][row[1]] = row[2]
                total += row[2]
        
        stats["total_notes"] = total
        return stats
    
    def list_recent_notes(self, limit: int = 10) -> List[Dict[str, Any]]:
        """List recently processed notes"""
        cursor = self.conn.execute("""
            SELECT note_path, note_title, note_type, created_at
            FROM notes
            ORDER BY created_at DESC
            LIMIT ?
        """, (limit,))
        
        return [
            {
                'path': row[0],
                'title': row[1],
                'type': row[2],
                'processed_at': row[3]
            }
            for row in cursor.fetchall()
        ]
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    if len(sys.argv) < 2:
        print("""
🧠 NotePlan RAG System (Simple Version)

Usage:
  python noteplan_rag_simple.py embed                    # Process all your notes
  python noteplan_rag_simple.py search "query"           # Search your notes
  python noteplan_rag_simple.py ask "question"           # Ask with RAG context
  python noteplan_rag_simple.py ask "question" r1        # Use specific model
  python noteplan_rag_simple.py stats                    # Show database stats
  python noteplan_rag_simple.py recent                   # Show recent notes

Examples:
  python noteplan_rag_simple.py ask "What are my thoughts on product strategy?"
  python noteplan_rag_simple.py ask "What did I learn about pricing?" r1
  python noteplan_rag_simple.py search "journey mapping"
        """)
        return
    
    rag = NotePlanRAG()
    
    try:
        command = sys.argv[1].lower()
        
        if command == "embed":
            rag.embed_all_notes()
        
        elif command == "search":
            if len(sys.argv) < 3:
                print("❌ Please provide a search query")
                return
            query = sys.argv[2]
            results = rag.search_notes(query)
            
            print(f"\n📋 Found {len(results)} relevant notes:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['title']} ({result['path']})")
                print(f"   Score: {result['score']:.4f}")
                print(f"   Preview: {result['content'][:200]}...")
        
        elif command == "ask":
            if len(sys.argv) < 3:
                print("❌ Please provide a question")
                return
            question = sys.argv[2]
            model = sys.argv[3] if len(sys.argv) > 3 else DEFAULT_LLM
            
            answer = rag.ask_with_context(question, model)
            print(f"\n🤖 Answer (using {model}):")
            print(answer)
        
        elif command == "stats":
            stats = rag.get_stats()
            print(f"\n📊 Database Statistics:")
            print(f"Total notes: {stats['total_notes']}")
            print("By type:")
            for note_type, count in stats['by_type'].items():
                print(f"  {note_type}: {count}")
        
        elif command == "recent":
            recent = rag.list_recent_notes()
            print(f"\n📝 Recently processed notes:")
            for note in recent:
                print(f"  {note['title']} ({note['type']}) - {note['processed_at']}")
        
        else:
            print(f"❌ Unknown command: {command}")
    
    finally:
        rag.close()

if __name__ == "__main__":
    main()
